"""
Refactored Monitoring Module
Focused monitoring components with single responsibilities
"""
import logging
import time
import psutil
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from abc import ABC, abstractmethod


@dataclass
class SystemMetrics:
    """System performance metrics data structure"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float


@dataclass
class ApplicationMetrics:
    """Application-specific metrics data structure"""
    timestamp: datetime
    active_connections: int
    request_count: int
    error_count: int
    avg_response_time: float


class MetricsCollector(ABC):
    """Abstract base class for metrics collectors"""
    
    @abstractmethod
    def collect(self) -> Dict[str, Any]:
        """Collect metrics and return as dictionary"""
        pass


class SystemMetricsCollector(MetricsCollector):
    """Collects system-level performance metrics"""
    
    def __init__(self):
        self.prev_disk_io = None
        self.prev_network_io = None
    
    def collect(self) -> SystemMetrics:
        """Collect current system metrics"""
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Disk I/O
        disk_io = psutil.disk_io_counters()
        disk_read_mb = 0
        disk_write_mb = 0
        
        if disk_io and self.prev_disk_io:
            disk_read_mb = (disk_io.read_bytes - self.prev_disk_io.read_bytes) / 1024 / 1024
            disk_write_mb = (disk_io.write_bytes - self.prev_disk_io.write_bytes) / 1024 / 1024
        
        self.prev_disk_io = disk_io
        
        # Network I/O
        network_io = psutil.net_io_counters()
        network_sent_mb = 0
        network_recv_mb = 0
        
        if network_io and self.prev_network_io:
            network_sent_mb = (network_io.bytes_sent - self.prev_network_io.bytes_sent) / 1024 / 1024
            network_recv_mb = (network_io.bytes_recv - self.prev_network_io.bytes_recv) / 1024 / 1024
        
        self.prev_network_io = network_io
        
        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / 1024 / 1024,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb
        )


class ApplicationMetricsCollector(MetricsCollector):
    """Collects application-level metrics"""
    
    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.response_times: List[float] = []
        self.active_connections = 0
        self.lock = threading.Lock()
    
    def collect(self) -> ApplicationMetrics:
        """Collect current application metrics"""
        with self.lock:
            # Calculate average response time
            avg_response_time = 0.0
            if self.response_times:
                avg_response_time = sum(self.response_times) / len(self.response_times)
                self.response_times.clear()  # Reset for next interval
            
            metrics = ApplicationMetrics(
                timestamp=datetime.now(),
                active_connections=self.active_connections,
                request_count=self.request_count,
                error_count=self.error_count,
                avg_response_time=avg_response_time
            )
            
            # Reset counters
            self.request_count = 0
            self.error_count = 0
            
            return metrics
    
    def record_request(self, response_time: float, is_error: bool = False):
        """Record a request for metrics"""
        with self.lock:
            self.request_count += 1
            self.response_times.append(response_time)
            
            if is_error:
                self.error_count += 1
    
    def increment_connections(self):
        """Increment active connection count"""
        with self.lock:
            self.active_connections += 1
    
    def decrement_connections(self):
        """Decrement active connection count"""
        with self.lock:
            self.active_connections = max(0, self.active_connections - 1)


class AlertRule:
    """Defines an alert rule with threshold and condition"""
    
    def __init__(self, name: str, condition: Callable[[Dict[str, Any]], bool], 
                 message_template: str, severity: str = 'WARNING'):
        self.name = name
        self.condition = condition
        self.message_template = message_template
        self.severity = severity
    
    def check(self, metrics: Dict[str, Any]) -> Optional[str]:
        """Check if alert condition is met"""
        try:
            if self.condition(metrics):
                return self.message_template.format(**metrics)
        except Exception:
            pass
        return None


class AlertManager:
    """Manages alert rules and notifications"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.rules: List[AlertRule] = []
        self.alert_history: List[Dict[str, Any]] = []
        self.max_history = 1000
    
    def add_rule(self, rule: AlertRule):
        """Add an alert rule"""
        self.rules.append(rule)
    
    def remove_rule(self, rule_name: str):
        """Remove an alert rule by name"""
        self.rules = [rule for rule in self.rules if rule.name != rule_name]
    
    def check_alerts(self, metrics: Dict[str, Any]):
        """Check all alert rules against metrics"""
        for rule in self.rules:
            alert_message = rule.check(metrics)
            if alert_message:
                self._trigger_alert(rule, alert_message, metrics)
    
    def _trigger_alert(self, rule: AlertRule, message: str, metrics: Dict[str, Any]):
        """Trigger an alert"""
        alert_data = {
            'timestamp': datetime.now(),
            'rule_name': rule.name,
            'severity': rule.severity,
            'message': message,
            'metrics': metrics.copy()
        }
        
        # Add to history
        self.alert_history.append(alert_data)
        if len(self.alert_history) > self.max_history:
            self.alert_history = self.alert_history[-self.max_history:]
        
        # Log the alert
        log_level = getattr(logging, rule.severity.upper(), logging.WARNING)
        self.logger.log(log_level, f"Performance alert: {message}", extra={
            'operation': 'performance_alert',
            'alert_rule': rule.name,
            'alert_severity': rule.severity
        })
    
    def get_recent_alerts(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get alerts from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [alert for alert in self.alert_history if alert['timestamp'] >= cutoff_time]


class MetricsStorage:
    """Stores and manages metrics history"""
    
    def __init__(self, max_history: int = 1440):  # 24 hours at 1-minute intervals
        self.max_history = max_history
        self.system_metrics: List[SystemMetrics] = []
        self.application_metrics: List[ApplicationMetrics] = []
        self.lock = threading.Lock()
    
    def store_system_metrics(self, metrics: SystemMetrics):
        """Store system metrics"""
        with self.lock:
            self.system_metrics.append(metrics)
            if len(self.system_metrics) > self.max_history:
                self.system_metrics = self.system_metrics[-self.max_history:]
    
    def store_application_metrics(self, metrics: ApplicationMetrics):
        """Store application metrics"""
        with self.lock:
            self.application_metrics.append(metrics)
            if len(self.application_metrics) > self.max_history:
                self.application_metrics = self.application_metrics[-self.max_history:]
    
    def get_recent_system_metrics(self, minutes: int = 60) -> List[SystemMetrics]:
        """Get system metrics from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        with self.lock:
            return [m for m in self.system_metrics if m.timestamp >= cutoff_time]
    
    def get_recent_application_metrics(self, minutes: int = 60) -> List[ApplicationMetrics]:
        """Get application metrics from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        with self.lock:
            return [m for m in self.application_metrics if m.timestamp >= cutoff_time]
    
    def get_summary_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """Get summary statistics for the last N minutes"""
        system_metrics = self.get_recent_system_metrics(minutes)
        app_metrics = self.get_recent_application_metrics(minutes)
        
        if not system_metrics and not app_metrics:
            return {}
        
        stats = {'period_minutes': minutes}
        
        if system_metrics:
            stats.update({
                'avg_cpu_percent': sum(m.cpu_percent for m in system_metrics) / len(system_metrics),
                'max_cpu_percent': max(m.cpu_percent for m in system_metrics),
                'avg_memory_percent': sum(m.memory_percent for m in system_metrics) / len(system_metrics),
                'max_memory_percent': max(m.memory_percent for m in system_metrics),
                'system_sample_count': len(system_metrics)
            })
        
        if app_metrics:
            stats.update({
                'total_requests': sum(m.request_count for m in app_metrics),
                'total_errors': sum(m.error_count for m in app_metrics),
                'avg_response_time': sum(m.avg_response_time for m in app_metrics) / len(app_metrics),
                'max_response_time': max(m.avg_response_time for m in app_metrics),
                'application_sample_count': len(app_metrics)
            })
        
        return stats


class PerformanceMonitor:
    """Focused performance monitor with single responsibility"""
    
    def __init__(self, logger: Optional[logging.Logger] = None, interval: float = 60.0):
        self.logger = logger or logging.getLogger(__name__)
        self.interval = interval
        
        # Components
        self.system_collector = SystemMetricsCollector()
        self.app_collector = ApplicationMetricsCollector()
        self.alert_manager = AlertManager(self.logger)
        self.storage = MetricsStorage()
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        
        # Setup default alert rules
        self._setup_default_alerts()
    
    def _setup_default_alerts(self):
        """Setup default alert rules"""
        # CPU alert
        self.alert_manager.add_rule(AlertRule(
            name='high_cpu',
            condition=lambda m: m.get('cpu_percent', 0) > 80,
            message_template='High CPU usage: {cpu_percent:.1f}%',
            severity='WARNING'
        ))
        
        # Memory alert
        self.alert_manager.add_rule(AlertRule(
            name='high_memory',
            condition=lambda m: m.get('memory_percent', 0) > 85,
            message_template='High memory usage: {memory_percent:.1f}%',
            severity='WARNING'
        ))
        
        # Response time alert
        self.alert_manager.add_rule(AlertRule(
            name='slow_response',
            condition=lambda m: m.get('avg_response_time', 0) > 5.0,
            message_template='Slow response time: {avg_response_time:.2f}s',
            severity='WARNING'
        ))
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Performance monitoring started", extra={
            'operation': 'monitoring_start',
            'interval': self.interval
        })
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("Performance monitoring stopped", extra={
            'operation': 'monitoring_stop'
        })
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Collect metrics
                system_metrics = self.system_collector.collect()
                app_metrics = self.app_collector.collect()
                
                # Store metrics
                self.storage.store_system_metrics(system_metrics)
                self.storage.store_application_metrics(app_metrics)
                
                # Combine metrics for alerting
                combined_metrics = {
                    **system_metrics.__dict__,
                    **app_metrics.__dict__
                }
                
                # Check alerts
                self.alert_manager.check_alerts(combined_metrics)
                
                # Log metrics
                self._log_metrics(system_metrics, app_metrics)
                
            except Exception as e:
                self.logger.error(f"Error in performance monitoring: {e}", extra={
                    'operation': 'monitoring_error',
                    'error_type': type(e).__name__
                })
            
            time.sleep(self.interval)
    
    def _log_metrics(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """Log collected metrics"""
        self.logger.info("Performance metrics collected", extra={
            'operation': 'performance_metrics',
            'cpu_percent': system_metrics.cpu_percent,
            'memory_percent': system_metrics.memory_percent,
            'memory_used_mb': system_metrics.memory_used_mb,
            'disk_read_mb': system_metrics.disk_io_read_mb,
            'disk_write_mb': system_metrics.disk_io_write_mb,
            'network_sent_mb': system_metrics.network_sent_mb,
            'network_recv_mb': system_metrics.network_recv_mb,
            'active_connections': app_metrics.active_connections,
            'request_count': app_metrics.request_count,
            'error_count': app_metrics.error_count,
            'avg_response_time': app_metrics.avg_response_time
        })
    
    def get_app_collector(self) -> ApplicationMetricsCollector:
        """Get application metrics collector"""
        return self.app_collector
    
    def get_alert_manager(self) -> AlertManager:
        """Get alert manager"""
        return self.alert_manager
    
    def get_storage(self) -> MetricsStorage:
        """Get metrics storage"""
        return self.storage
