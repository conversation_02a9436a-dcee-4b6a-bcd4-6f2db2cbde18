"""
Installation context and utilities

This module provides context classes and utilities for addon installation operations.
"""
import time
from typing import Optional, List, TYPE_CHECKING

if TYPE_CHECKING:
    from ...environment import Environment

from ...logging import get_logger

logger = get_logger(__name__)


class InstallationContext:
    """Context for addon installation operations"""
    
    def __init__(self, addon_name: str, env: 'Environment', operation: str = 'install'):
        self.addon_name = addon_name
        self.env = env
        self.operation = operation
        self.start_time = time.perf_counter()
        self.logger = get_logger(f"{__name__}.{addon_name}")
    
    def get_duration(self) -> float:
        """Get elapsed time since context creation"""
        return time.perf_counter() - self.start_time
    
    def log_success(self, message: str = None) -> None:
        """Log successful operation"""
        duration = self.get_duration()
        if message is None:
            message = f"Successfully {self.operation}ed addon {self.addon_name}"
        self.logger.info(f"✅ {message} in {duration:.3f}s")
    
    def log_error(self, error: Exception, message: str = None) -> None:
        """Log failed operation"""
        duration = self.get_duration()
        if message is None:
            message = f"Failed to {self.operation} addon {self.addon_name}"
        self.logger.error(f"❌ {message} after {duration:.3f}s: {error}")
    
    def log_step(self, step: str) -> None:
        """Log installation step"""
        self.logger.debug(f"{step} for {self.addon_name}")


class RegistryExtractor:
    """Utility class for extracting information from model registries"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def extract_tables_from_registry(self, model_registry) -> List[str]:
        """Extract table names from the ModelRegistry"""
        try:
            models_with_fields = model_registry.get_all_models_with_fields()
            tables = []
            for _, model_info in models_with_fields.items():
                table_name = model_info.get('table')
                if table_name:
                    tables.append(table_name)
            return tables
        except Exception as e:
            self.logger.error(f"Failed to extract tables from registry: {e}")
            return []
    
    def extract_models_from_registry(self, model_registry) -> List[str]:
        """Extract model names from the ModelRegistry"""
        try:
            return model_registry.get_model_names()
        except Exception as e:
            self.logger.error(f"Failed to extract models from registry: {e}")
            return []
    
    def extract_constraints_from_registry(self, model_registry) -> List[str]:
        """Extract constraint names from the ModelRegistry based on model tables"""
        try:
            models_with_fields = model_registry.get_all_models_with_fields()
            constraints = []

            # Generate expected constraint names based on table names and specific patterns
            for model_name, model_info in models_with_fields.items():
                table_name = model_info.get('table')
                if table_name:
                    # Use specific constraint patterns for known base tables
                    if table_name == 'ir_module_module':
                        constraints.append('uk_ir_module_module_name')
                    elif table_name == 'ir_model':
                        constraints.append('uk_ir_model_name')
                    elif table_name == 'ir_model_fields':
                        constraints.append('uk_ir_model_fields_model_name')
                    else:
                        # Default pattern for other tables
                        constraints.append(f"uk_{table_name}_name")

            return constraints
        except Exception as e:
            self.logger.error(f"Failed to extract constraints from registry: {e}")
            return []
    
    def extract_indexes_from_registry(self, model_registry) -> List[str]:
        """Extract index names from the ModelRegistry based on model tables and fields"""
        try:
            models_with_fields = model_registry.get_all_models_with_fields()
            indexes = []

            # Generate expected index names based on specific base table patterns
            for model_name, model_info in models_with_fields.items():
                table_name = model_info.get('table')
                fields = model_info.get('fields', {})

                if table_name:
                    # Use specific index patterns for known base tables
                    if table_name == 'ir_model_fields':
                        indexes.extend(['idx_ir_model_fields_model', 'idx_ir_model_fields_name'])
                    elif table_name == 'ir_module_module':
                        indexes.append('idx_ir_module_module_state')
                    elif table_name == 'ir_model':
                        indexes.append('idx_ir_model_state')
                    else:
                        # Default patterns for other tables
                        if 'model' in fields:
                            indexes.append(f"idx_{table_name}_model")
                        if 'name' in fields:
                            indexes.append(f"idx_{table_name}_name")
                        if 'state' in fields:
                            indexes.append(f"idx_{table_name}_state")

            return indexes
        except Exception as e:
            self.logger.error(f"Failed to extract indexes from registry: {e}")
            return []


class InstallationUtilities:
    """Common utilities for installation operations"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.registry_extractor = RegistryExtractor()
        
        # Initialize shared utilities
        from ...utils import get_registry_updater, get_validation_manager
        self._registry_updater = get_registry_updater()
        self._validation_manager = get_validation_manager()
    
    async def update_registry_after_action(self, env: 'Environment', addon_name: str, action: str):
        """Update the in-memory registry after a module action"""
        await self._registry_updater.update_registry_after_module_action(env, addon_name, action)
    
    def get_lifecycle_manager(self):
        """Get the centralized addon lifecycle manager"""
        from ..lifecycle_manager import get_addon_lifecycle_manager
        return get_addon_lifecycle_manager()
    
    def get_validation_manager(self):
        """Get the shared validation manager"""
        return self._validation_manager
