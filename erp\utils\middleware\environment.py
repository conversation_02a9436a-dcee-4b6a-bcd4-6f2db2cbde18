import logging
from fastapi import Request
from typing import Callable, Any
from ...environment.manager import Environment<PERSON>anager
from ...context.manager import Con<PERSON><PERSON>anager
from ..responses import handle_generic_error

logger = logging.getLogger(__name__)

class EnvironmentMiddleware:
    """Sets up the ERP Environment for each request."""

    @staticmethod
    async def process_request(request: Request, call_next: Callable) -> Any:
        """Processes the request and sets up the ERP environment."""
        # Import here to avoid circular imports
        from .database import RouteClassifier

        # Skip environment middleware for system routes (same as database middleware)
        if not RouteClassifier.needs_database_middleware(request.url.path):
            logger.debug(f"⏭️ Skipping environment middleware for system path: {request.url.path}")
            return await call_next(request)

        db_name = getattr(request.state, 'db_name', None)
        if not db_name:
            logger.warning("No database name found in request state")
            return await call_next(request)

        uid = getattr(request.state, 'user_id', None)
        if uid is None:
            uid = request.headers.get('X-User-ID')
            if uid:
                try:
                    uid = int(uid)
                except ValueError:
                    uid = None

            if uid is None:
                uid_param = request.query_params.get('uid')
                if uid_param:
                    try:
                        uid = int(uid_param)
                    except ValueError:
                        uid = None

        if uid is None:
            uid = 1
            logger.debug("No user ID specified, defaulting to admin (uid=1)")

        context = {}
        lang = request.headers.get('Accept-Language', 'en_US')
        if lang:
            context['lang'] = lang.split(',')[0].replace('-', '_')

        tz = request.headers.get('X-Timezone')
        if tz:
            context['tz'] = tz

        for header_name, header_value in request.headers.items():
            if header_name.lower().startswith('x-context-'):
                context_key = header_name[10:].lower()
                context[context_key] = header_value

        try:
            env = await EnvironmentManager.create_environment(db_name, uid, context)
            async with ContextManager.with_context(env=env):
                request.state.env = env
                response = await call_next(request)
                return response
        except Exception as e:
            logger.error(f"Error creating environment: {e}")
            raise handle_generic_error(e)